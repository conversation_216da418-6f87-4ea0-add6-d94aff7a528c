"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, ArrowRight, Volume2, CheckCircle, RotateCcw, Play } from "lucide-react"
import Link from "next/link"

const moduleContent = {
  1: {
    title: "Introduction to Sounds",
    description: "Basic phoneme awareness and sound identification",
    activities: [
      {
        type: "instruction",
        title: "What are Sounds?",
        content:
          "Every word is made up of individual sounds called phonemes. Let's learn to hear and identify these sounds.",
      },
      {
        type: "interactive",
        title: "Sound Identification",
        content: "Listen to each sound and click on the matching picture.",
        sounds: ["/a/", "/m/", "/s/", "/t/"],
      },
      {
        type: "practice",
        title: "Sound Matching",
        content: "Match the sound you hear to the correct letter.",
        exercises: 5,
      },
    ],
  },
}

export default function ModulePage({ params }: { params: { id: string } }) {
  const [currentActivity, setCurrentActivity] = useState(0)
  const [progress, setProgress] = useState(0)
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null)
  const [showFeedback, setShowFeedback] = useState(false)
  const [isCorrect, setIsCorrect] = useState(false)

  const moduleId = Number.parseInt(params.id)
  const module = moduleContent[moduleId as keyof typeof moduleContent]

  if (!module) {
    return <div>Module not found</div>
  }

  const currentActivityData = module.activities[currentActivity]
  const totalActivities = module.activities.length

  const handleNext = () => {
    if (currentActivity < totalActivities - 1) {
      setCurrentActivity(currentActivity + 1)
      setProgress(((currentActivity + 1) / totalActivities) * 100)
      setSelectedAnswer(null)
      setShowFeedback(false)
    }
  }

  const handlePrevious = () => {
    if (currentActivity > 0) {
      setCurrentActivity(currentActivity - 1)
      setProgress(((currentActivity - 1) / totalActivities) * 100)
      setSelectedAnswer(null)
      setShowFeedback(false)
    }
  }

  const handleAnswerSelect = (answer: string) => {
    setSelectedAnswer(answer)
    setShowFeedback(true)
    // Simple logic for demonstration - in real app, this would check against correct answers
    setIsCorrect(Math.random() > 0.5)
  }

  const playSound = (sound: string) => {
    // In a real app, this would play the actual sound
    console.log(`Playing sound: ${sound}`)
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" asChild>
            <Link href="/lessons/pink">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">{module.title}</h2>
            <p className="text-muted-foreground">{module.description}</p>
          </div>
        </div>
        <Badge className="bg-pink-100 text-pink-800">Pink Level</Badge>
      </div>

      {/* Progress Bar */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Progress</span>
            <span className="text-sm text-muted-foreground">
              {currentActivity + 1} of {totalActivities} activities
            </span>
          </div>
          <Progress value={progress} className="w-full" />
        </CardContent>
      </Card>

      {/* Activity Content */}
      <Card className="min-h-[400px]">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            {currentActivityData.title}
            <Badge variant="outline">{currentActivityData.type}</Badge>
          </CardTitle>
          <CardDescription>{currentActivityData.content}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {currentActivityData.type === "instruction" && (
            <div className="text-center space-y-4">
              <div className="bg-muted rounded-lg p-8">
                <h3 className="text-xl font-semibold mb-4">Learning Concept</h3>
                <p className="text-lg">{currentActivityData.content}</p>
              </div>
            </div>
          )}

          {currentActivityData.type === "interactive" && currentActivityData.sounds && (
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-4">Click on a sound to hear it:</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {currentActivityData.sounds.map((sound, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="lg"
                      className="h-20 text-2xl"
                      onClick={() => playSound(sound)}
                    >
                      <Volume2 className="mr-2 h-6 w-6" />
                      {sound}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="text-center">
                <h3 className="text-lg font-semibold mb-4">Now match the sound to the picture:</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {["Apple", "Mouse", "Sun", "Tree"].map((item, index) => (
                    <Card
                      key={index}
                      className={`cursor-pointer transition-all ${
                        selectedAnswer === item
                          ? showFeedback
                            ? isCorrect
                              ? "border-green-500 bg-green-50"
                              : "border-red-500 bg-red-50"
                            : "border-blue-500 bg-blue-50"
                          : "hover:border-gray-400"
                      }`}
                      onClick={() => handleAnswerSelect(item)}
                    >
                      <CardContent className="p-4 text-center">
                        <div className="w-16 h-16 mx-auto mb-2 bg-gray-200 rounded-lg flex items-center justify-center">
                          <span className="text-2xl">
                            {item === "Apple" ? "🍎" : item === "Mouse" ? "🐭" : item === "Sun" ? "☀️" : "🌳"}
                          </span>
                        </div>
                        <p className="font-medium">{item}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {showFeedback && (
                <div
                  className={`text-center p-4 rounded-lg ${isCorrect ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
                >
                  <div className="flex items-center justify-center space-x-2">
                    {isCorrect ? <CheckCircle className="h-5 w-5" /> : <RotateCcw className="h-5 w-5" />}
                    <span className="font-medium">
                      {isCorrect ? "Correct! Great job!" : "Try again! Listen carefully to the sound."}
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}

          {currentActivityData.type === "practice" && (
            <div className="text-center space-y-6">
              <div className="bg-muted rounded-lg p-8">
                <h3 className="text-xl font-semibold mb-4">Practice Exercise</h3>
                <p className="text-lg mb-6">{currentActivityData.content}</p>
                <Button size="lg">
                  <Play className="mr-2 h-5 w-5" />
                  Start Practice
                </Button>
              </div>
              <div className="text-sm text-muted-foreground">
                Complete {currentActivityData.exercises} exercises to continue
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={handlePrevious} disabled={currentActivity === 0}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Previous
        </Button>

        <Button onClick={handleNext} disabled={currentActivity === totalActivities - 1}>
          Next
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
