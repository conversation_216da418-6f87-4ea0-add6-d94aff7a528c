import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Search, Plus, Filter, Download, Eye } from "lucide-react"
import Link from "next/link"

const learners = [
  {
    id: 1,
    name: "<PERSON>",
    age: 7,
    level: "<PERSON>",
    progress: 85,
    tutor: "<PERSON>",
    lastSession: "2024-01-15",
    status: "Active",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: 2,
    name: "<PERSON>",
    age: 8,
    level: "<PERSON>",
    progress: 72,
    tutor: "<PERSON>",
    lastSession: "2024-01-14",
    status: "Active",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: 3,
    name: "<PERSON>",
    age: 6,
    level: "<PERSON>",
    progress: 45,
    tutor: "<PERSON>",
    lastSession: "2024-01-13",
    status: "Active",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: 4,
    name: "<PERSON> <PERSON>",
    age: 9,
    level: "Yellow",
    progress: 60,
    tutor: "David <PERSON>",
    lastSession: "2024-01-12",
    status: "Active",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: 5,
    name: "Ava Johnson",
    age: 10,
    level: "Purple",
    progress: 90,
    tutor: "Sarah <PERSON>",
    lastSession: "2024-01-11",
    status: "Completed",
    avatar: "/placeholder.svg?height=40&width=40",
  },
]

const getLevelColor = (level: string) => {
  switch (level) {
    case "Pink":
      return "bg-pink-100 text-pink-800"
    case "Blue":
      return "bg-blue-100 text-blue-800"
    case "Yellow":
      return "bg-yellow-100 text-yellow-800"
    case "Purple":
      return "bg-purple-100 text-purple-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "Active":
      return "bg-green-100 text-green-800"
    case "Completed":
      return "bg-blue-100 text-blue-800"
    case "Inactive":
      return "bg-gray-100 text-gray-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

export default function LearnersPage() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Learner Management</h2>
        <div className="flex items-center space-x-2">
          <Button asChild>
            <Link href="/learners/enroll">
              <Plus className="mr-2 h-4 w-4" />
              Enroll New Learner
            </Link>
          </Button>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input placeholder="Search learners..." className="pl-8" />
              </div>
            </div>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Learners List */}
      <Card>
        <CardHeader>
          <CardTitle>All Learners</CardTitle>
          <CardDescription>Manage and track progress of all enrolled learners</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {learners.map((learner) => (
              <div key={learner.id} className="flex items-center space-x-4 rounded-lg border p-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={learner.avatar || "/placeholder.svg"} alt={learner.name} />
                  <AvatarFallback>
                    {learner.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1 space-y-1">
                  <div className="flex items-center space-x-2">
                    <h3 className="font-semibold">{learner.name}</h3>
                    <Badge className={getLevelColor(learner.level)}>{learner.level} Level</Badge>
                    <Badge className={getStatusColor(learner.status)}>{learner.status}</Badge>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <span>Age: {learner.age}</span>
                    <span>Tutor: {learner.tutor}</span>
                    <span>Last Session: {learner.lastSession}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">Progress:</span>
                    <Progress value={learner.progress} className="w-32" />
                    <span className="text-sm font-medium">{learner.progress}%</span>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/learners/${learner.id}`}>
                      <Eye className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
