import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from "recharts"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Users, BookOpen, Clock, TrendingUp, Award, Target, Download, FileText } from "lucide-react"

const tutorData = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    specialization: "Early Reading",
    activeLearners: 12,
    totalLearners: 45,
    avgImprovement: 78,
    rating: 4.8,
    experience: "5 years",
    certifications: ["Phono-Graphix Certified", "Reading Specialist"],
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    specialization: "Advanced Decoding",
    activeLearners: 8,
    totalLearners: 32,
    avgImprovement: 82,
    rating: 4.9,
    experience: "3 years",
    certifications: ["Phono-Graphix Certified"],
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: 3,
    name: "Emily Davis",
    email: "<EMAIL>",
    specialization: "Learning Differences",
    activeLearners: 15,
    totalLearners: 58,
    avgImprovement: 75,
    rating: 4.7,
    experience: "7 years",
    certifications: ["Phono-Graphix Certified", "Special Education", "Dyslexia Specialist"],
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: 4,
    name: "David Wilson",
    email: "<EMAIL>",
    specialization: "Multisyllabic Words",
    activeLearners: 10,
    totalLearners: 38,
    avgImprovement: 80,
    rating: 4.8,
    experience: "4 years",
    certifications: ["Phono-Graphix Certified", "Literacy Coach"],
    avatar: "/placeholder.svg?height=40&width=40",
  },
]

const performanceData = [
  { name: "Sarah Johnson", improvement: 78, learners: 12 },
  { name: "Mike Chen", improvement: 82, learners: 8 },
  { name: "Emily Davis", improvement: 75, learners: 15 },
  { name: "David Wilson", improvement: 80, learners: 10 },
]

export default function TutorsPage() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Tutor Tools</h2>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Reports
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Tutors</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8</div>
            <p className="text-xs text-muted-foreground">2 new this month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Learners</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">45</div>
            <p className="text-xs text-muted-foreground">Across all tutors</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Improvement</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">79%</div>
            <p className="text-xs text-muted-foreground">+5% from last quarter</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Rating</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4.8</div>
            <p className="text-xs text-muted-foreground">Out of 5.0</p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Tutor Performance Overview</CardTitle>
          <CardDescription>Learner improvement rates by tutor</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer
            config={{
              improvement: {
                label: "Improvement %",
                color: "hsl(var(--chart-1))",
              },
            }}
            className="h-[300px]"
          >
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={performanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Bar dataKey="improvement" fill="var(--color-improvement)" />
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>

      {/* Tutor Directory */}
      <Card>
        <CardHeader>
          <CardTitle>Tutor Directory</CardTitle>
          <CardDescription>Detailed information about all tutors and their performance</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {tutorData.map((tutor) => (
              <div key={tutor.id} className="flex items-start space-x-4 rounded-lg border p-6">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={tutor.avatar || "/placeholder.svg"} alt={tutor.name} />
                  <AvatarFallback>
                    {tutor.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1 space-y-3">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="text-lg font-semibold">{tutor.name}</h3>
                      <p className="text-sm text-muted-foreground">{tutor.email}</p>
                      <p className="text-sm text-muted-foreground">Specialization: {tutor.specialization}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Award className="h-4 w-4 text-yellow-500" />
                      <span className="font-medium">{tutor.rating}</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-sm font-medium">Active Learners</p>
                      <p className="text-2xl font-bold text-blue-600">{tutor.activeLearners}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Total Taught</p>
                      <p className="text-2xl font-bold text-green-600">{tutor.totalLearners}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Avg Improvement</p>
                      <p className="text-2xl font-bold text-purple-600">{tutor.avgImprovement}%</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Experience</p>
                      <p className="text-2xl font-bold text-orange-600">{tutor.experience}</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <p className="text-sm font-medium">Certifications:</p>
                    <div className="flex flex-wrap gap-2">
                      {tutor.certifications.map((cert, index) => (
                        <Badge key={index} variant="secondary">
                          {cert}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex flex-col space-y-2">
                  <Button variant="outline" size="sm">
                    <FileText className="mr-2 h-4 w-4" />
                    View Details
                  </Button>
                  <Button variant="outline" size="sm">
                    <Target className="mr-2 h-4 w-4" />
                    Performance
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="mr-2 h-5 w-5" />
              Performance Tracking
            </CardTitle>
            <CardDescription>Monitor individual tutor performance and learner outcomes</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full">Access Performance Tools</Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="mr-2 h-5 w-5" />
              Resource Library
            </CardTitle>
            <CardDescription>Access Phono-Graphix materials and teaching resources</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full">Browse Resources</Button>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="mr-2 h-5 w-5" />
              Lesson Planning
            </CardTitle>
            <CardDescription>Plan and schedule lessons for your learners</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full">Plan Lessons</Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
